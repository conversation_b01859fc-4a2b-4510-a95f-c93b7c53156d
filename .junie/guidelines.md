# Project Guidelines
Following rules are in `./junie/` folder.
Project is using <PERSON><PERSON> and has `./mvn` folder.
If you run a build or compile of the project, first run `ktlint:format`

## Default
The file `architecture-rules.md` contains the basic architectural rules that apply to every new code. Always read them.

## Query
If your task is to write EP / logic that returns data, i.e., Q in CQRS, use `query-rules.md` and `jooq-rules.md`.

## JOOQ
If your task is to write only SQL JOOQ, thoroughly read through `jooq-rules.md`.

## Command
If you are creating new logic, i.e., C in CQRS, thoroughly read through `command-rules.md`. If you are using events, read through `event-rules.md`.

## Cron
If your task is to create a cron job, read through `cron-rules.md`.

## Domain
Whenever you interfere with the domain, read through `entity-rules.md`.

## Test
For writing tests, you only need to read `test-rules.md`.
To run tests in command line, use `./mvnw` and if needed run multiple tests at once instead of one-by-one.
