package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.gamepayoutoffset.command.AdminPatchesGamePayoutOffsetCommand
import java.math.BigDecimal
import java.time.Year

data class AdminPatchesGamePayoutOffsetRequest(
    val offset: BigDecimal,
) {
    fun toCommand(year: Year = Year.now()) = AdminPatchesGamePayoutOffsetCommand(
        year = year,
        offset = offset,
    )
}
