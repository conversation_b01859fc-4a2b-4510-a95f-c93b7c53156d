package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.TraderPatchesLocationRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.TraderPatchesPrivacyRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.TraderUpdatesProfileRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.user.trader.query.GetMyTraderProfileQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Trader [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/traders/me")
class TraderMeController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.TRADER_ROLE)
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getMyTraderProfile(@AuthenticationPrincipal traderId: UUID): GetMyTraderProfileQuery.Result =
        queryBus(GetMyTraderProfileQuery(traderId = traderId))

    @Operation(
        description = """
            Trader updates their profile.
        """,
    )
    @RolesAllowed(UserRole.TRADER_ROLE)
    @PutMapping("/profile", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun traderUpdatesProfile(@RequestBody request: TraderUpdatesProfileRequest): Unit = commandBus(request.toCommand())

    @Operation(
        description = """
            Trader patches their privacy settings.
        """,
    )
    @RolesAllowed(UserRole.TRADER_ROLE)
    @PatchMapping("/privacy", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun traderPatchesPrivacy(
        @AuthenticationPrincipal traderId: UUID,
        @RequestBody request: TraderPatchesPrivacyRequest,
    ): Unit = commandBus(request.toCommand(traderId = traderId))

    @Operation(
        description = """
            Trader patches their location.
        """,
    )
    @RolesAllowed(UserRole.TRADER_ROLE)
    @PatchMapping("/location", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun traderPatchesLocation(
        @AuthenticationPrincipal traderId: UUID,
        @RequestBody request: TraderPatchesLocationRequest,
    ): Unit = commandBus(request.toCommand(traderId = traderId))
}
