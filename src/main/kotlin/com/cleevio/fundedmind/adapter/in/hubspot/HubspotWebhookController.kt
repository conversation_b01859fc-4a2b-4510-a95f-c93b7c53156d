package com.cleevio.fundedmind.adapter.`in`.hubspot

import com.cleevio.fundedmind.adapter.`in`.hubspot.request.HubspotChangedProperty
import com.cleevio.fundedmind.adapter.`in`.hubspot.request.UserUpdatedFromHubspotRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import io.swagger.v3.oas.annotations.Hidden
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Hidden
@RestController
@RequestMapping("/hubspot")
class HubspotWebhookController(private val commandBud: CommandBus) {

    @PostMapping("/user/webhook")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun userWebhook(@RequestBody request: List<UserUpdatedFromHubspotRequest>) {
        request.forEach {
            when (it.propertyChanged) {
                HubspotChangedProperty.BAN -> commandBud(it.toBanUserCommand())
                HubspotChangedProperty.TIER -> commandBud(it.toUpdateStudentTierCommand())
            }
        }
    }
}
