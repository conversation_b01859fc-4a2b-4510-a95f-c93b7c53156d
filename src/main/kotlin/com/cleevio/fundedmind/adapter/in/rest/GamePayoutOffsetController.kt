package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.AdminPatchesCurrentGamePayoutOffsetRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Game Payout Offset [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game-payout-offset")
class GamePayoutOffsetController(
    private val commandBus: CommandBus,
) {

    @Operation(
        description = """
            Admin patches current game payout offset.
            Updates the offset value for the specified year (defaults to current year).
            404 - Game payout offset for the specified year not found. (should not happen)
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PatchMapping("/current", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun adminPatchesCurrentGamePayoutOffset(@RequestBody request: AdminPatchesCurrentGamePayoutOffsetRequest): Unit =
        commandBus(request.toCommand())
}
