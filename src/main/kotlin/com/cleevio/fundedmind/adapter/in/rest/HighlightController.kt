package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateNewHighlightRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.ReorderHighlightsRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.UpdateHighlightRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.highlight.command.DeleteHighlightCommand
import com.cleevio.fundedmind.application.module.highlight.command.HideHighlightCommand
import com.cleevio.fundedmind.application.module.highlight.command.PublishHighlightCommand
import com.cleevio.fundedmind.application.module.highlight.query.GetHighlightDetailQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Highlight [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/highlights")
class HighlightController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.ADMIN_ROLE)
    @GetMapping("/{highlightId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getHighlightDetail(@PathVariable highlightId: UUID): GetHighlightDetailQuery.Result =
        queryBus(GetHighlightDetailQuery(highlightId = highlightId))

    @Operation(
        description = """
            Admin creates new highlight.
            400 - button is present but linkUrl is null
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createHighlight(@RequestBody request: CreateNewHighlightRequest): IdResult = commandBus(request.toCommand())

    @Operation(
        description = """
            Admin updates a highlight.
            400 - button is present but linkUrl is null
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/{highlightId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateHighlight(
        @PathVariable highlightId: UUID,
        @RequestBody request: UpdateHighlightRequest,
    ): Unit = commandBus(request.toCommand(highlightId = highlightId))

    @Operation(
        description = """
            Admin deletes a highlight.
            404 - Highlight not found
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @DeleteMapping("/{highlightId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteHighlight(@PathVariable highlightId: UUID): Unit =
        commandBus(DeleteHighlightCommand(highlightId = highlightId))

    @Operation(
        description = """
            Admin publishes highlight to students.
            422 - Highlight does not have pictures uploaded.
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/{highlightId}/publish", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun publishHighlight(@PathVariable highlightId: UUID) =
        commandBus(PublishHighlightCommand(highlightId = highlightId))

    @Operation(
        description = """
            Admin hides highlight from students.
        """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PostMapping("/{highlightId}/hide", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun hideHighlight(@PathVariable highlightId: UUID) = commandBus(HideHighlightCommand(highlightId = highlightId))

    @Operation(
        description = """
            Admin changes display order of highlights (Drag&Drop table).
            400 - display order is not positive or zero
            422 - payload does not contain all highlights (highlights might have been added or removed since last fetch)
	    """,
    )
    @RolesAllowed(UserRole.ADMIN_ROLE)
    @PutMapping("/reorder", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun reorderHighlights(@RequestBody request: ReorderHighlightsRequest): Unit = commandBus(request.toCommand())
}
