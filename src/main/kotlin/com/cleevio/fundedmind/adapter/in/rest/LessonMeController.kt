package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.course.command.CheckUserAccessToLessonCommand
import com.cleevio.fundedmind.application.module.lesson.query.GetModuleLessonPlaylistQuery
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Lesson [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/courses/{courseId}/modules/{courseModuleId}/lessons/me")
class LessonMeController(
    private val queryBus: QueryBus,
    private val commandBus: CommandBus,
) {

    @Operation(
        description = """
            System checks if logged in user has access to lesson.
            403 - user has no access to lesson
        """,
    )
    @GetMapping("/{lessonId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun checkUserAccessToLesson(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
        @PathVariable lessonId: UUID,
    ): Unit = commandBus(
        CheckUserAccessToLessonCommand(
            userId = userId,
            courseId = courseId,
            courseModuleId = courseModuleId,
            lessonId = lessonId,
        ),
    )

    @Operation(
        description = """
            User gets playlist of lessons in module.
            403 - course is locked to the user
            404 - course module not found / course not published / module or course deleted
        """,
    )
    @GetMapping("/playlist", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getModuleLessonPlaylist(
        @AuthenticationPrincipal userId: UUID,
        @PathVariable courseId: UUID,
        @PathVariable courseModuleId: UUID,
    ): GetModuleLessonPlaylistQuery.Result = queryBus(
        GetModuleLessonPlaylistQuery(
            userId = userId,
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )
}
