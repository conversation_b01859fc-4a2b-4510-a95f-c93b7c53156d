package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.gamepayoutoffset.command.AdminPatchesCurrentGamePayoutOffsetCommand
import java.math.BigDecimal
import java.time.Year

data class AdminPatchesCurrentGamePayoutOffsetRequest(
    val year: Year = Year.now(),
    val offset: BigDecimal,
) {
    fun toCommand() = AdminPatchesCurrentGamePayoutOffsetCommand(
        year = year,
        offset = offset,
    )
}
