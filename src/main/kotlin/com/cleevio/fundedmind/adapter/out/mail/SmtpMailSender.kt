package com.cleevio.fundedmind.adapter.out.mail

import com.cleevio.fundedmind.application.common.port.out.SendEmailPort
import com.cleevio.fundedmind.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.beans.factory.annotation.Value
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

@Service
class SmtpMailSender(
    private val mailSender: JavaMailSender,
    @Value("\${spring.mail.properties.mail.smtp.from}") private val sendFrom: String,
) : SendEmailPort {

    private val logger = logger()

    @SentryTransaction(operation = "async.mail.send-email")
    @Async
    override fun sendEmailAsync(
        sendTo: String,
        subject: String,
        emailBody: String,
    ) {
        runCatching {
            mailSender.send { mimeMessage ->
                MimeMessageHelper(
                    /* mimeMessage = */
                    mimeMessage,
                    /* multipart = */
                    true,
                    /* encoding = */
                    Charsets.UTF_8.displayName(),
                ).apply {
                    setFrom(sendFrom)
                    addTo(sendTo)
                    setSubject(subject)
                    setText(emailBody, true)
                }
            }

            logger.info("Successfully sent an email to: '$sendTo', with subject: '$subject'")
        }.onFailure { exception ->
            logger.error("Failed to send an email to: '$sendTo', with subject: '$subject'", exception)
        }
    }
}
