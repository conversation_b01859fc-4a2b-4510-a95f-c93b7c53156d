package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.gamedocument.port.out.GetPayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamedocument.query.GetPayoutOverviewQuery
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.jooq.tables.references.GAME_DOCUMENT
import com.cleevio.fundedmind.jooq.tables.references.GAME_PAYOUT_OFFSET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Year

@Component
class GetPayoutOverviewJooq(
    private val dslContext: DSLContext,
) : GetPayoutOverviewPort {

    override fun invoke(year: Year): GetPayoutOverviewQuery.Result {
        // Get approved payouts count and sum for the year
        val approvedPayoutsData = dslContext
            .select(
                DSL.count().`as`("approved_count"),
                DSL.coalesce(DSL.sum(GAME_DOCUMENT.PAYOUT_AMOUNT), BigDecimal.ZERO).`as`("real_total_payout"),
            )
            .from(GAME_DOCUMENT)
            .where(
                GAME_DOCUMENT.TYPE.eq(GameDocumentType.PAYOUT)
                    .and(GAME_DOCUMENT.STATE.eq(GameDocumentApprovalState.APPROVED))
                    .and(DSL.extract(GAME_DOCUMENT.PAYOUT_DATE, DSL.keyword("YEAR")).eq(year.value)),
            )
            .fetchOne()

        val approvedPayouts = approvedPayoutsData?.get("approved_count", Int::class.java) ?: 0
        val realTotalPayout = approvedPayoutsData?.get("real_total_payout", BigDecimal::class.java) ?: BigDecimal.ZERO

        // Get offset for the year (default to 0 if not found)
        val offset = dslContext
            .select(GAME_PAYOUT_OFFSET.PAYOUT_OFFSET)
            .from(GAME_PAYOUT_OFFSET)
            .where(GAME_PAYOUT_OFFSET.YEAR.eq(year.value))
            .fetchOne(GAME_PAYOUT_OFFSET.PAYOUT_OFFSET) ?: BigDecimal.ZERO

        val offsetTotalPayout = realTotalPayout.add(offset)

        return GetPayoutOverviewQuery.Result(
            offsetTotalPayout = offsetTotalPayout,
            realTotalPayout = realTotalPayout,
            approvedPayouts = approvedPayouts,
        )
    }
}
