package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.port.out.AutocompleteStudentUserPort
import com.cleevio.fundedmind.application.module.user.appuser.query.AutocompleteStudentUserQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.jooq.tables.references.APP_USER
import com.cleevio.fundedmind.jooq.tables.references.STUDENT
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class AutocompleteStudentUserJooq(private val dslContext: DSLContext) : AutocompleteStudentUserPort {

    override fun invoke(
        filter: AutocompleteStudentUserQuery.Filter,
        limit: Int,
    ): AutocompleteStudentUserQuery.Result {
        require(limit > 0)

        val conditions = listOfNotNull(
            APP_USER.ROLE.eq(UserRole.STUDENT),
            filter.autocompleteCondition(APP_USER.EMAIL),
        )

        return dslContext
            .select(
                APP_USER.ID,
                APP_USER.EMAIL,
                STUDENT.FIRST_NAME,
                STUDENT.LAST_NAME,
                STUDENT.studentProfilePicture.ID,
                STUDENT.studentProfilePicture.ORIGINAL_FILE_URL,
                STUDENT.studentProfilePicture.COMPRESSED_FILE_URL,
                STUDENT.studentProfilePicture.BLUR_HASH,
            )
            .from(APP_USER)
            .innerJoin(STUDENT).on(APP_USER.ID.eq(STUDENT.ID))
            .where(conditions)
            .orderBy(APP_USER.EMAIL.asc())
            .limit(limit)
            .fetch()
            .map {
                AutocompleteStudentUserQuery.AutocompleteStudentUser(
                    userId = it[APP_USER.ID]!!,
                    email = it[APP_USER.EMAIL]!!,
                    firstName = it[STUDENT.FIRST_NAME],
                    lastName = it[STUDENT.LAST_NAME],
                    profilePicture = it[STUDENT.studentProfilePicture.ID]?.let { imageId ->
                        ImageResult(
                            imageId = imageId,
                            imageOriginalUrl = it[STUDENT.studentProfilePicture.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = it[STUDENT.studentProfilePicture.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = it[STUDENT.studentProfilePicture.BLUR_HASH]!!,
                        )
                    },
                )
            }
            .let { AutocompleteStudentUserQuery.Result(data = it) }
    }
}
