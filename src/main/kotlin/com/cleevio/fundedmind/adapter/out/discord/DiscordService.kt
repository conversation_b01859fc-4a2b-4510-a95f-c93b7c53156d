package com.cleevio.fundedmind.adapter.out.discord

import com.cleevio.fundedmind.application.common.port.out.ConnectDiscordUserToGuildPort
import com.cleevio.fundedmind.application.common.port.out.DiscordConnectUserPortRequest
import com.cleevio.fundedmind.application.common.port.out.DiscordUserPortResponse
import com.cleevio.fundedmind.application.common.port.out.DiscordUserRoleUpdatePort
import com.cleevio.fundedmind.application.common.port.out.toPortResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class DiscordService(
    private val discordConnector: DiscordConnector,
    @Value("\${integration.discord.studentRoleId}") private val premiumStudentRoleId: String,
) : ConnectDiscordUserToGuildPort, DiscordUserRoleUpdatePort {

    override fun connectUserToGuild(request: DiscordConnectUserPortRequest): DiscordUserPortResponse =
        discordConnector.addGuildMember(
            userId = request.discordUserId,
            accessToken = request.accessToken,
            firstName = request.userName,
            lastName = request.lastName,
            hasPremiumDiscordAccess = request.hasDiscordPremiumAccess,
        )
            ?.toPortResponse()
            ?: getDiscordUserFromGuildAndUpdateRoles(
                discordUserId = request.discordUserId,
                hasDiscordPremiumAccess = request.hasDiscordPremiumAccess,
            )

    override fun grantDiscordUserRole(discordUserId: String) {
        discordConnector.addPremiumStudentRoleToGuildMember(userId = discordUserId)
    }

    override fun revokeDiscordUserRole(discordUserId: String) {
        discordConnector.removePremiumRolesFromGuildMember(userId = discordUserId)
    }

    private fun getDiscordUserFromGuildAndUpdateRoles(
        discordUserId: String,
        hasDiscordPremiumAccess: Boolean,
    ): DiscordUserPortResponse {
        val discordUser = discordConnector.getGuildMember(discordUserId).toPortResponse()

        if (hasPremiumAccessButRoleMissingInDiscord(hasDiscordPremiumAccess, discordUser)) {
            grantDiscordUserRole(discordUser.discordId)
        } else if (hasNoPremiumAccessButHasRoleInDiscord(hasDiscordPremiumAccess, discordUser)) {
            revokeDiscordUserRole(discordUser.discordId)
        }

        return discordUser
    }

    private fun hasPremiumAccessButRoleMissingInDiscord(
        hasDiscordPremiumAccess: Boolean,
        discordUser: DiscordUserPortResponse,
    ) = hasDiscordPremiumAccess && !discordUser.hasPremiumStudentRole(premiumStudentRoleId)

    private fun hasNoPremiumAccessButHasRoleInDiscord(
        hasDiscordPremiumAccess: Boolean,
        discordUser: DiscordUserPortResponse,
    ) = !hasDiscordPremiumAccess && discordUser.hasPremiumStudentRole(premiumStudentRoleId)
}
