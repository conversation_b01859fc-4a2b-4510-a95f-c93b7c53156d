package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.coursemodule.port.out.ListCourseModulesInCoursePort
import com.cleevio.fundedmind.application.module.coursemodule.query.ListCourseModulesInCourseQuery
import com.cleevio.fundedmind.jooq.tables.references.COURSE_MODULE
import com.cleevio.fundedmind.jooq.tables.references.LESSON
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component

@Component
class ListCourseModulesInCourseJooq(
    private val dslContext: DSLContext,
) : ListCourseModulesInCoursePort {

    override fun invoke(filter: ListCourseModulesInCourseQuery.Filter): ListCourseModulesInCourseQuery.Result {
        val conditions = listOfNotNull(
            COURSE_MODULE.DELETED_AT.isNull(),
            COURSE_MODULE.COURSE_ID.eq(filter.courseId),
            filter.autocompleteCondition(
                COURSE_MODULE.TITLE,
            ),
        )

        return dslContext
            .select(
                COURSE_MODULE.ID,
                COURSE_MODULE.LISTING_ORDER,
                COURSE_MODULE.TITLE,
                COURSE_MODULE.REWARD_BUTTON_LINK_URL,
                COURSE_MODULE.REWARD_COUPON_CODE,
                COURSE_MODULE.COMING_SOON,
                lessons,
                COURSE_MODULE.thumbnailPictureDesktop.ID,
                COURSE_MODULE.thumbnailPictureDesktop.ORIGINAL_FILE_URL,
                COURSE_MODULE.thumbnailPictureDesktop.COMPRESSED_FILE_URL,
                COURSE_MODULE.thumbnailPictureDesktop.BLUR_HASH,
                COURSE_MODULE.thumbnailPictureMobile.ID,
                COURSE_MODULE.thumbnailPictureMobile.ORIGINAL_FILE_URL,
                COURSE_MODULE.thumbnailPictureMobile.COMPRESSED_FILE_URL,
                COURSE_MODULE.thumbnailPictureMobile.BLUR_HASH,
            )
            .from(COURSE_MODULE)
            .where(conditions)
            .orderBy(COURSE_MODULE.LISTING_ORDER.asc())
            .fetch()
            .map(::mapSearchCourseModules)
            .let { ListCourseModulesInCourseQuery.Result(data = it) }
    }

    private val lessons = multiset(
        select(
            LESSON.ID,
            LESSON.DURATION_IN_SECONDS,
        )
            .from(LESSON)
            .where(LESSON.COURSE_MODULE_ID.eq(COURSE_MODULE.ID))
            .and(LESSON.DELETED_AT.isNull()),
    ).`as`("lessons")

    private fun mapSearchCourseModules(record: Record) = ListCourseModulesInCourseQuery.CourseModuleListing(
        courseModuleId = record[COURSE_MODULE.ID]!!,
        listingOrder = record[COURSE_MODULE.LISTING_ORDER]!!,
        title = record[COURSE_MODULE.TITLE]!!,
        reward = determineIfRewardPresent(record),
        comingSoon = record[COURSE_MODULE.COMING_SOON]!!,
        lessonCount = record[lessons].size,
        totalDurationInSeconds = record[lessons].sumOf { it[LESSON.DURATION_IN_SECONDS]!! },
        thumbnailDesktop = record[COURSE_MODULE.thumbnailPictureDesktop.ID]?.let { imageId ->
            ImageResult(
                imageId = imageId,
                imageOriginalUrl = record[COURSE_MODULE.thumbnailPictureDesktop.ORIGINAL_FILE_URL]!!,
                imageCompressedUrl = record[COURSE_MODULE.thumbnailPictureDesktop.COMPRESSED_FILE_URL]!!,
                imageBlurHash = record[COURSE_MODULE.thumbnailPictureDesktop.BLUR_HASH]!!,
            )
        },
        thumbnailMobile = record[COURSE_MODULE.thumbnailPictureMobile.ID]?.let { imageId ->
            ImageResult(
                imageId = imageId,
                imageOriginalUrl = record[COURSE_MODULE.thumbnailPictureMobile.ORIGINAL_FILE_URL]!!,
                imageCompressedUrl = record[COURSE_MODULE.thumbnailPictureMobile.COMPRESSED_FILE_URL]!!,
                imageBlurHash = record[COURSE_MODULE.thumbnailPictureMobile.BLUR_HASH]!!,
            )
        },
    )

    private fun determineIfRewardPresent(record: Record): Boolean {
        val rewardButtonLink = record[COURSE_MODULE.REWARD_BUTTON_LINK_URL]
        val rewardCouponCode = record[COURSE_MODULE.REWARD_COUPON_CODE]

        return rewardButtonLink != null || rewardCouponCode != null
    }
}
