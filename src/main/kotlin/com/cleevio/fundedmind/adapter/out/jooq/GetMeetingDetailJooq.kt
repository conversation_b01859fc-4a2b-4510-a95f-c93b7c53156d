package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.meeting.port.out.GetMeetingDetailPort
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingDetailQuery
import com.cleevio.fundedmind.domain.meeting.exception.MeetingNotFoundException
import com.cleevio.fundedmind.jooq.tables.references.MEETING
import com.cleevio.fundedmind.jooq.tables.references.TRADER_IN_MEETING
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetMeetingDetailJooq(
    private val dslContext: DSLContext,
) : GetMeetingDetailPort {

    override fun invoke(meetingId: UUID): GetMeetingDetailQuery.Result {
        val conditions = listOfNotNull(
            MEETING.ID.eq(meetingId),
        )

        return dslContext
            .select(
                MEETING.ID,
                MEETING.NAME,
                MEETING.COLOR,
                MEETING.START_AT,
                MEETING.FINISH_AT,
                MEETING.DESCRIPTION,
                MEETING.INVITED_TIERS,
                MEETING.INVITED_DISCORD_USERS,
                MEETING.MEETING_URL,
                MEETING.RECORDING_URL,
                MEETING.coverPhoto.ID,
                MEETING.coverPhoto.ORIGINAL_FILE_URL,
                MEETING.coverPhoto.COMPRESSED_FILE_URL,
                MEETING.coverPhoto.BLUR_HASH,
                traders,
            )
            .from(MEETING)
            .where(conditions)
            .fetchOne()
            ?.map { record ->
                GetMeetingDetailQuery.Result(
                    meetingId = record[MEETING.ID]!!,
                    name = record[MEETING.NAME]!!,
                    color = record[MEETING.COLOR]!!,
                    startAt = record[MEETING.START_AT]!!,
                    finishAt = record[MEETING.FINISH_AT]!!,
                    description = record[MEETING.DESCRIPTION]!!,
                    invitedTiers = record[MEETING.INVITED_TIERS]!!,
                    invitedDiscordUsers = record[MEETING.INVITED_DISCORD_USERS]!!,
                    meetingUrl = record[MEETING.MEETING_URL],
                    recordingUrl = record[MEETING.RECORDING_URL],
                    coverPhoto = record[MEETING.coverPhoto.ID]?.let { coverPhotoId ->
                        ImageResult(
                            imageId = coverPhotoId,
                            imageOriginalUrl = record[MEETING.coverPhoto.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = record[MEETING.coverPhoto.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = record[MEETING.coverPhoto.BLUR_HASH]!!,
                        )
                    },
                    traders = record[traders].map(::mapTraderBio),
                )
            }
            ?: throw MeetingNotFoundException("Meeting: '$meetingId' not found")
    }

    private val traders = multiset(
        select(
            TRADER_IN_MEETING.DISPLAY_ORDER,
            TRADER_IN_MEETING.trader.ID,
            TRADER_IN_MEETING.trader.POSITION,
            TRADER_IN_MEETING.trader.FIRST_NAME,
            TRADER_IN_MEETING.trader.LAST_NAME,
            TRADER_IN_MEETING.trader.traderProfilePicture.ID,
            TRADER_IN_MEETING.trader.traderProfilePicture.ORIGINAL_FILE_URL,
            TRADER_IN_MEETING.trader.traderProfilePicture.COMPRESSED_FILE_URL,
            TRADER_IN_MEETING.trader.traderProfilePicture.BLUR_HASH,
        )
            .from(TRADER_IN_MEETING)
            .where(TRADER_IN_MEETING.MEETING_ID.eq(MEETING.ID))
            .orderBy(TRADER_IN_MEETING.DISPLAY_ORDER.asc()),
    )

    private fun mapTraderBio(traderRecord: Record) = GetMeetingDetailQuery.TraderInMeetingBio(
        traderId = traderRecord[TRADER_IN_MEETING.trader.ID]!!,
        position = traderRecord[TRADER_IN_MEETING.trader.POSITION]!!,
        displayOrder = traderRecord[TRADER_IN_MEETING.DISPLAY_ORDER]!!,
        firstName = traderRecord[TRADER_IN_MEETING.trader.FIRST_NAME]!!,
        lastName = traderRecord[TRADER_IN_MEETING.trader.LAST_NAME]!!,
        profilePicture = traderRecord[TRADER_IN_MEETING.trader.traderProfilePicture.ID]?.let { profilePictureId ->
            ImageResult(
                imageId = profilePictureId,
                imageOriginalUrl = traderRecord[TRADER_IN_MEETING.trader.traderProfilePicture.ORIGINAL_FILE_URL]!!,
                imageCompressedUrl = traderRecord[TRADER_IN_MEETING.trader.traderProfilePicture.COMPRESSED_FILE_URL]!!,
                imageBlurHash = traderRecord[TRADER_IN_MEETING.trader.traderProfilePicture.BLUR_HASH]!!,
            )
        },
    )
}
