package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.referral.command.PublishReferralCommand
import com.cleevio.fundedmind.application.module.referral.finder.ReferralFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class PublishReferralCommandHandler(
    private val referralFinderService: ReferralFinderService,
) : CommandHandler<Unit, PublishReferralCommand> {

    override val command = PublishReferralCommand::class

    @Transactional
    @Lock(module = Locks.Referral.MODULE, lockName = Locks.Referral.UPDATE)
    override fun handle(@LockFieldParameter("referralId") command: PublishReferralCommand) {
        referralFinderService
            .getById(command.referralId)
            .apply { publish() }
    }
}
