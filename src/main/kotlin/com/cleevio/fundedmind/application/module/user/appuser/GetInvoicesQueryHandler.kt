package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.application.common.port.out.PaymentInvoicePort
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.appuser.query.GetInvoicesQuery
import org.springframework.stereotype.Component

@Component
class GetInvoicesQueryHandler(
    private val appUserFinderService: AppUserFinderService,
    private val invoicePort: PaymentInvoicePort,
) : QueryHandler<GetInvoicesQuery.Result, GetInvoicesQuery> {

    override val query = GetInvoicesQuery::class

    override fun handle(query: GetInvoicesQuery): GetInvoicesQuery.Result {
        val customerIds: List<StripeCustomerId> = appUserFinderService.getById(query.userId).stripeIdentifiers

        return invoicePort
            .listFinalizedInvoicesByCustomerIdIn(customerIds)
            .sortedByDescending { it.invoicedAt }
            .let { GetInvoicesQuery.Result(it) }
    }
}
