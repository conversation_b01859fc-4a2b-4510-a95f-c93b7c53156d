package com.cleevio.fundedmind.application.module.user.appuser

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.port.out.UserActiveStatusPort
import com.cleevio.fundedmind.application.common.util.ifNotEmpty
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.mentoring.finder.MentoringFinderService
import com.cleevio.fundedmind.application.module.mentoringmeeting.finder.MentoringMeetingFinderService
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.appuser.command.DisableTraderAccountCommand
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasMentoringMeetingToAttendException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasSaleableProductException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasUnfinishedMentoringException
import com.cleevio.fundedmind.domain.mentoring.Mentoring
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeeting
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Component
class DisableTraderAccountCommandHandler(
    private val appUserFinderService: AppUserFinderService,
    private val productFinderService: ProductFinderService,
    private val mentoringFinderService: MentoringFinderService,
    private val mentoringMeetingFinderService: MentoringMeetingFinderService,
    private val userActiveStatusPort: UserActiveStatusPort,
) : CommandHandler<Unit, DisableTraderAccountCommand> {

    override val command = DisableTraderAccountCommand::class

    @Transactional
    @Lock(module = Locks.AppUser.MODULE, lockName = Locks.AppUser.UPDATE)
    override fun handle(@LockFieldParameter("traderId") command: DisableTraderAccountCommand) {
        val userId = command.traderId
        appUserFinderService.getById(userId)
            .apply {
                checkRole(UserRole.TRADER)
                checkTraderHasNoSaleableProduct(command.traderId)
                checkTraderHasNoUnfinishedMentorings(command.traderId)
                checkTraderHasNoMentoringMeetingsToAttendInFuture(command.traderId, command.now)
                disableAccount()
            }
            .also { userActiveStatusPort.disableUser(it.email) }
    }

    private fun checkTraderHasNoSaleableProduct(traderId: UUID) {
        productFinderService.existsSaleableNonDeletedByTraderId(traderId).ifTrue {
            throw TraderHasSaleableProductException(
                "Cannot disable trader: '$traderId' because they have saleable product.",
            )
        }
    }

    private fun checkTraderHasNoUnfinishedMentorings(traderId: UUID) {
        mentoringFinderService
            .findAllByTraderId(traderId)
            .filter { it.hasSessionsLeft }
            .ifNotEmpty {
                throw TraderHasUnfinishedMentoringException(
                    "Cannot disable trader: '$traderId' because " +
                        "they have unfinished mentorings: '${map(Mentoring::id)}'.",
                )
            }
    }

    private fun checkTraderHasNoMentoringMeetingsToAttendInFuture(
        traderId: UUID,
        now: Instant,
    ) {
        mentoringMeetingFinderService
            .findAllByTraderId(traderId)
            .filter { !it.isModified }
            .filter { it.finishAt > now }
            .ifNotEmpty {
                throw TraderHasMentoringMeetingToAttendException(
                    "Cannot disable trader: '$traderId' because " +
                        "they have mentoring meetings to attend in future: '${map(MentoringMeeting::id)}'.",
                )
            }
    }
}
