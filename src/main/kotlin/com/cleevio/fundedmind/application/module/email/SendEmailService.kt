package com.cleevio.fundedmind.application.module.email

import com.cleevio.fundedmind.application.common.port.out.GetEmailTemplatePort
import com.cleevio.fundedmind.application.common.port.out.GetUserPort
import com.cleevio.fundedmind.application.common.port.out.SendEmailPort
import com.cleevio.fundedmind.application.common.util.composeLessonUrl
import com.cleevio.fundedmind.application.common.util.composeNetworkingMessageUrl
import com.cleevio.fundedmind.application.module.comment.finder.ThreadCommentNotificationFinderService
import com.cleevio.fundedmind.application.module.comment.port.out.GetCommentForNotificationPort
import com.cleevio.fundedmind.application.module.mentoring.finder.MentoringFinderService
import com.cleevio.fundedmind.application.module.networking.finder.NetworkingMessageFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.VerificationCodeFinderService
import com.cleevio.fundedmind.application.module.user.appuser.query.GetUserQuery
import com.cleevio.fundedmind.application.module.user.student.finder.OnboardingFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.infrastructure.properties.FundedMindUrlPrefixProperties
import com.github.jknack.handlebars.Handlebars
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.UUID

@Service
class SendEmailService(
    private val handlebars: Handlebars,
    private val getEmailTemplatePort: GetEmailTemplatePort,
    private val sendEmailPort: SendEmailPort,
    private val studentFinderService: StudentFinderService,
    private val onboardingFinderService: OnboardingFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val verificationCodeFinderService: VerificationCodeFinderService,
    private val mentoringFinderService: MentoringFinderService,
    private val traderFinderService: TraderFinderService,
    private val fundedMindUrlPrefixProperties: FundedMindUrlPrefixProperties,
    private val getUserPort: GetUserPort,
    private val threadCommentNotificationFinderService: ThreadCommentNotificationFinderService,
    private val getCommentForNotificationPort: GetCommentForNotificationPort,
    private val networkingMessageFinderService: NetworkingMessageFinderService,
) {

    fun sendEmailFinishOnboardingBasecamp(studentId: UUID) {
        val student = studentFinderService.getById(studentId)

        val user = appUserFinderService.getById(student.id)

        val appEmail = AppEmail.FinishOnboardingBasecamp(
            studentFirstNameVocative = student.fullName,
        )

        appEmail.sendEmail(sendTo = user.email)
    }

    fun sendEmailFinishOnboardingMasterclass(studentId: UUID) {
        val student = studentFinderService.getById(studentId)

        val user = appUserFinderService.getById(student.id)

        val appEmail = AppEmail.FinishOnboardingMasterclass(
            studentFirstNameVocative = student.fullName,
        )

        appEmail.sendEmail(sendTo = user.email)
    }

    fun sendEmailExclusiveUpgraded(studentId: UUID) {
        val student = studentFinderService.getById(studentId)

        val user = appUserFinderService.getById(student.id)

        val appEmail = AppEmail.ExclusiveUpgraded(
            studentFirstNameVocative = student.fullName,
        )

        appEmail.sendEmail(sendTo = user.email)
    }

    fun sendEmailOtpEmail(verificationCodeId: UUID) {
        val verificationCode = verificationCodeFinderService.getById(verificationCodeId)

        val name = getUserFirstNameVocative(verificationCode.appUserId)
        val user: GetUserQuery.Result = getUserPort(verificationCode.appUserId)

        val appEmail = AppEmail.OtpEmail(
            userName = name,
            code = verificationCode.code,
            expiresAt = verificationCode.expiresAt,
        )

        appEmail.sendEmail(sendTo = user.email)
    }

    fun sendEmailStudentFinishesMentoringCheckout(mentoringId: UUID) {
        val mentoring = mentoringFinderService.getById(mentoringId)
        val trader = traderFinderService.getByProductId(mentoring.productId)

        val student = studentFinderService.getById(mentoring.studentId)
        val studentUser = appUserFinderService.getById(mentoring.studentId)

        val appEmail = AppEmail.StudentFinishesMentoringCheckout(
            studentFirstNameVocative = student.firstNameVocative,
            traderFullName = trader.fullName,
            productName = mentoring.productName,
            productAltDescription = mentoring.productAltDescription,
        )

        appEmail.sendEmail(sendTo = studentUser.email)
    }

    fun sendEmailTraderFinishesMentoringCheckout(mentoringId: UUID) {
        val mentoring = mentoringFinderService.getById(mentoringId)

        val student = studentFinderService.getById(mentoring.studentId)
        val studentUser = appUserFinderService.getById(mentoring.studentId)

        val trader = traderFinderService.getByProductId(mentoring.productId)
        val traderUser = appUserFinderService.getById(trader.id)

        val appEmail = AppEmail.TraderFinishesMentoringCheckout(
            productName = mentoring.productName,
            studentFullName = student.fullName,
            studentEmail = studentUser.email,
            studentPhone = student.phone,
        )

        appEmail.sendEmail(sendTo = traderUser.email)
    }

    fun sendEmailDiscordCancelled(
        studentId: UUID,
        endsAt: Instant,
    ) {
        val student = studentFinderService.getById(studentId)
        val studentUser = appUserFinderService.getById(studentId)

        val appEmail = AppEmail.DiscordCancelled(
            studentFirstNameVocative = student.firstNameVocative,
            endsAt = endsAt,
        )

        appEmail.sendEmail(sendTo = studentUser.email)
    }

    fun sendEmailDiscordEnded(studentId: UUID) {
        val student = studentFinderService.getById(studentId)
        val studentUser = appUserFinderService.getById(studentId)

        val appEmail = AppEmail.DiscordEnded(
            studentFirstNameVocative = student.firstNameVocative,
        )

        appEmail.sendEmail(sendTo = studentUser.email)
    }

    fun sendEmailDiscordPaymentFailed(
        studentId: UUID,
        endsAt: Instant,
    ) {
        val student = studentFinderService.getById(studentId)
        val studentUser = appUserFinderService.getById(studentId)

        val appEmail = AppEmail.DiscordPaymentFailed(
            studentFirstNameVocative = student.firstNameVocative,
            endsAt = endsAt,
        )

        appEmail.sendEmail(sendTo = studentUser.email)
    }

    fun sendTestEmail(emailTo: String) {
        val appEmail = AppEmail.FinishOnboardingBasecamp(
            studentFirstNameVocative = "FIRST LAST",
        )

        appEmail.sendEmail(sendTo = emailTo)
    }

    fun sendEmailNewThreadComment(threadCommentNotificationId: UUID) {
        val notification = threadCommentNotificationFinderService.getById(threadCommentNotificationId)

        val name = getUserFirstNameVocative(notification.appUserId)
        val user = getUserPort(userId = notification.appUserId)

        val comment = getCommentForNotificationPort(commentId = notification.threadId)

        // Compose the lesson URL
        val lessonUrl = composeLessonUrl(
            urlPrefix = fundedMindUrlPrefixProperties.lesson,
            courseId = comment.courseId,
            courseModuleId = comment.courseModuleId,
            lessonId = comment.lessonId,
            threadId = comment.threadId ?: comment.commentId,
        ).removePrefix("https://") // FE requires value without protocol prefix

        val appEmail = AppEmail.CommentThreadReaction(
            studentName = name,
            lessonUrl = lessonUrl,
        )

        appEmail.sendEmail(sendTo = user.email)
    }

    fun sendNetworkingMessage(networkingMessageId: UUID) {
        val networkingMessage = networkingMessageFinderService.getById(networkingMessageId)

        val sendingUser = appUserFinderService.getById(networkingMessage.senderUserId)
        val recipientUser = appUserFinderService.getById(networkingMessage.recipientUserId)

        val senderName = getUserFirstName(sendingUser.id)
        val recipientName = getUserFirstName(recipientUser.id)

        // Compose the networking message URL
        val messageUrl = composeNetworkingMessageUrl(
            urlPrefix = fundedMindUrlPrefixProperties.networking,
            messageId = networkingMessage.id,
        ).removePrefix("https://") // FE requires value without protocol prefix

        val appEmail = AppEmail.NetworkingDirectMessage(
            senderUserName = senderName,
            recipientUserName = recipientName,
            message = networkingMessage.textShortened,
            messageUrl = messageUrl,
        )

        appEmail.sendEmail(sendTo = recipientUser.email)
    }

    private fun AppEmail.sendEmail(sendTo: String) {
        val template: String = getEmailTemplatePort(this.type)

        val message = template.fillWith(this.placeholders)

        sendEmailPort.sendEmailAsync(
            sendTo = sendTo,
            subject = this.subject,
            emailBody = message,
        )
    }

    private fun getUserFirstName(userId: UUID): String {
        val user = appUserFinderService.getById(userId)
        return when (user.role) {
            UserRole.STUDENT -> {
                val student = studentFinderService.getById(user.id)
                student.firstName
            }

            UserRole.TRADER -> {
                val trader = traderFinderService.getById(user.id)
                trader.firstName
            }

            UserRole.ADMIN -> "Admin"
        }
    }

    private fun getUserFirstNameVocative(userId: UUID): String {
        val user: GetUserQuery.Result = getUserPort(userId)
        return when (user.role) {
            UserRole.STUDENT -> {
                if (user.onboardingFinished) {
                    studentFinderService.getById(user.userId).firstNameVocative
                } else {
                    onboardingFinderService
                        .getById(user.userId)
                        .firstNameVocative
                        ?: ""
                }
            }

            UserRole.TRADER -> traderFinderService.getById(user.userId).firstName

            UserRole.ADMIN -> "Admin"
        }
    }

    private fun String.fillWith(placeholders: Map<String, Any>): String =
        handlebars.compileInline(this).apply(placeholders)
}
