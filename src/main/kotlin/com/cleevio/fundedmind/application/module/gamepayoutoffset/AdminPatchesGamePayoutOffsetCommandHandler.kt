package com.cleevio.fundedmind.application.module.gamepayoutoffset

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.gamepayoutoffset.command.AdminPatchesGamePayoutOffsetCommand
import com.cleevio.fundedmind.application.module.gamepayoutoffset.finder.GamePayoutOffsetFinderService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchesGamePayoutOffsetCommandHandler(
    private val gamePayoutOffsetFinderService: GamePayoutOffsetFinderService,
) : CommandHandler<Unit, AdminPatchesGamePayoutOffsetCommand> {
    override val command = AdminPatchesGamePayoutOffsetCommand::class

    @Transactional
    override fun handle(command: AdminPatchesGamePayoutOffsetCommand) {
        gamePayoutOffsetFinderService
            .getByYear(command.year)
            .updatePayoutOffset(command.offset)
    }
}
