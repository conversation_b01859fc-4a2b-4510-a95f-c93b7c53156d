package com.cleevio.fundedmind.application.module.gamedocument.query

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Year
import java.util.UUID

data class GetGameDocumentDetailQuery(
    val gameDocumentId: UUID,
) : Query<GetGameDocumentDetailQuery.Result> {

    @Schema(name = "GetGameDocumentDetailResult")
    data class Result(
        val gameDocumentId: UUID,
        val studentId: UUID,
        val type: GameDocumentType,
        val issuingCompany: IssuingCompany,
        val payoutAmount: BigDecimal?,
        val reachedLevel: GameLevel,
        val payoutDate: LocalDate,
        val gameDocumentFile: ImageResult?,
        val truthScore: Int,
        val state: GameDocumentApprovalState,
        val denyMessage: String?,
        val scoreMessage: String?,
    )
}

data class SearchGameDocumentsQuery(
    @field:Valid val infiniteScroll: InfiniteScroll<UUID>,
    @field:Valid val filter: Filter,
) : Query<InfiniteScrollSlice<SearchGameDocumentsQuery.Result, UUID>> {

    data class Filter(
        val studentId: UUID?,
        val state: GameDocumentApprovalState?,
    )

    @Schema(name = "SearchGameDocumentsResult")
    data class Result(
        val gameDocumentId: UUID,
        val student: SearchGameDocumentStudent,
        val type: GameDocumentType,
        val issuingCompany: IssuingCompany,
        val payoutAmount: BigDecimal?,
        val reachedLevel: GameLevel,
        val payoutDate: LocalDate,
        val gameDocumentFile: ImageResult?,
        val truthScore: Int,
        val state: GameDocumentApprovalState,
        val denyMessage: String?,
    )

    @Schema(name = "SearchGameDocumentStudent")
    data class SearchGameDocumentStudent(
        val studentId: UUID,
        val firstName: String,
        val lastName: String,
        val email: String,
        val profilePicture: ImageResult?,
        val currentGameLevel: GameLevel,
    )
}

data class GetPayoutOverviewQuery(
    val year: Year = Year.now(),
) : Query<GetPayoutOverviewQuery.Result> {

    @Schema(name = "GetPayoutOverviewResult")
    data class Result(
        val offsetTotalPayout: BigDecimal,
        val realTotalPayout: BigDecimal,
        val approvedPayouts: Int,
    )
}
