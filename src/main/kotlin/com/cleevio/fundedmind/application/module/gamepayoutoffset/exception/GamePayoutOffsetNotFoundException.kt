package com.cleevio.fundedmind.application.module.gamepayoutoffset.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class GamePayoutOffsetNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_PAYOUT_OFFSET_NOT_FOUND,
    message = message,
)
