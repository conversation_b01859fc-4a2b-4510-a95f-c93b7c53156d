package com.cleevio.fundedmind.application.module.gamepayoutoffset.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.gamepayoutoffset.exception.GamePayoutOffsetNotFoundException
import com.cleevio.fundedmind.domain.gamepayoutoffset.GamePayoutOffset
import com.cleevio.fundedmind.domain.gamepayoutoffset.GamePayoutOffsetRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Year

@Service
@Transactional(readOnly = true)
class GamePayoutOffsetFinderService(
    private val gamePayoutOffsetRepository: GamePayoutOffsetRepository,
) : BaseFinderService<GamePayoutOffset>(gamePayoutOffsetRepository) {

    override fun errorBlock(message: String) = throw GamePayoutOffsetNotFoundException(message)

    override fun getEntityType() = GamePayoutOffset::class

    fun findByYear(year: Year): GamePayoutOffset? = gamePayoutOffsetRepository.findByYear(year)

    fun getByYear(year: Year): GamePayoutOffset = findByYear(year)
        ?: throw GamePayoutOffsetNotFoundException("Game payout offset for year: '$year' not found.")
}
