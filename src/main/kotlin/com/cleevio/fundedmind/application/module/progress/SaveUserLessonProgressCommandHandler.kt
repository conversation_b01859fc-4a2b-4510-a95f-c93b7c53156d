package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.progress.command.SaveUserLessonProgressCommand
import com.cleevio.fundedmind.application.module.progress.port.out.SaveUserLessonProgressPort
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class SaveUserLessonProgressCommandHandler(
    private val saveUserLessonProgressPort: SaveUserLessonProgressPort,
) : CommandHandler<Unit, SaveUserLessonProgressCommand> {

    override val command = SaveUserLessonProgressCommand::class

    @Transactional
    @Lock(module = Locks.Progress.MODULE, lockName = Locks.Progress.UPDATE)
    override fun handle(@LockFieldParameter("userId") command: SaveUserLessonProgressCommand) {
        saveUserLessonProgressPort(
            userId = command.userId,
            lessonId = command.lessonId,
            seconds = command.seconds,
        )
    }
}
