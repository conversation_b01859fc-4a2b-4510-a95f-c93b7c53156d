package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.coursemodule.port.out.ListCourseModulesInCoursePort
import com.cleevio.fundedmind.application.module.coursemodule.query.ListCourseModulesInCourseQuery
import org.springframework.stereotype.Component

@Component
class ListCourseModulesInCourseQueryHandler(
    val listCourseModulesInCoursePort: ListCourseModulesInCoursePort,
) : QueryHandler<ListCourseModulesInCourseQuery.Result, ListCourseModulesInCourseQuery> {

    override val query = ListCourseModulesInCourseQuery::class

    override fun handle(query: ListCourseModulesInCourseQuery): ListCourseModulesInCourseQuery.Result =
        listCourseModulesInCoursePort(filter = query.filter)
}
