package com.cleevio.fundedmind.application.module.gamepayoutoffset

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.gamepayoutoffset.command.AdminPatchesCurrentGamePayoutOffsetCommand
import com.cleevio.fundedmind.application.module.gamepayoutoffset.finder.GamePayoutOffsetFinderService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AdminPatchesCurrentGamePayoutOffsetCommandHandler(
    private val gamePayoutOffsetFinderService: GamePayoutOffsetFinderService,
) : CommandHandler<Unit, AdminPatchesCurrentGamePayoutOffsetCommand> {
    override val command = AdminPatchesCurrentGamePayoutOffsetCommand::class

    @Transactional
    override fun handle(command: AdminPatchesCurrentGamePayoutOffsetCommand) {
        gamePayoutOffsetFinderService
            .getByYear(command.year)
            .updatePayoutOffset(command.offset)
    }
}
