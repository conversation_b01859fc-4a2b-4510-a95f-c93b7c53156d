package com.cleevio.fundedmind.domain.gamedocument

import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@Service
class CreateGameDocumentService(
    private val gameDocumentRepository: GameDocumentRepository,
) {

    @Transactional
    fun create(
        studentId: UUID,
        type: GameDocumentType,
        issuingCompany: IssuingCompany,
        amount: BigDecimal?,
        reachedLevel: GameLevel,
        payoutDate: LocalDate,
        truthScore: Int,
        scoreMessage: String?,
    ): GameDocument = gameDocumentRepository.save(
        GameDocument.newGameDocument(
            studentId = studentId,
            type = type,
            issuingCompany = issuingCompany,
            payoutAmount = amount,
            reachedLevel = reachedLevel,
            payoutDate = payoutDate,
            truthScore = truthScore,
            scoreMessage = scoreMessage,
        ),
    )
}
