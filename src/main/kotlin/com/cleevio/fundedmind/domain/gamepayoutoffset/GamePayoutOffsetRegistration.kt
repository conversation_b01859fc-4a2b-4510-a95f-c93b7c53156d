package com.cleevio.fundedmind.domain.gamepayoutoffset

import com.cleevio.fundedmind.application.module.gamepayoutoffset.finder.GamePayoutOffsetFinderService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Year

@Component
@Profile("!test")
class GamePayoutOffsetRegistration(
    private val createGamePayoutOffsetService: CreateGamePayoutOffsetService,
    private val gamePayoutOffsetFinderService: GamePayoutOffsetFinderService,
) {
    private val logger = logger()

    @Async
    @EventListener(ApplicationReadyEvent::class)
    fun onApplicationReady() {
        runCatching {
            logger.debug("Verifying presence of GamePayoutOffset entries...")

            val currentYear = Year.now()
            val nextYear = currentYear.plusYears(1)

            verifyYearEntry(currentYear)
            verifyYearEntry(nextYear)

            logger.debug("GamePayoutOffset entries verification completed.")
        }.onFailure {
            logger.error("Failed to verify GamePayoutOffset entries, but continuing application startup: $it", it)
        }
    }

    private fun verifyYearEntry(year: Year) {
        val existingEntry = gamePayoutOffsetFinderService.findByYear(year)

        if (existingEntry != null) {
            logger.debug("GamePayoutOffset for year: '$year' has offset: '${existingEntry.payoutOffset}'")
            return
        }

        logger.debug("Creating GamePayoutOffset for year: '$year' with offset: '0'")
        createGamePayoutOffsetService.create(
            year = year,
            offset = BigDecimal.ZERO,
        )
    }
}
