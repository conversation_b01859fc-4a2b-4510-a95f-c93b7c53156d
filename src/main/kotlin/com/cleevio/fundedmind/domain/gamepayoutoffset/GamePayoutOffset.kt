package com.cleevio.fundedmind.domain.gamepayoutoffset

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Year
import java.util.UUID

@Table(name = "game_payout_offset")
@Entity
@DynamicUpdate
class GamePayoutOffset private constructor(
    id: UUID,
    val year: Year,
    payoutOffset: BigDecimal,
) : DomainEntity(id) {

    var payoutOffset: BigDecimal = payoutOffset
        private set

    companion object {
        fun newGamePayoutOffset(
            id: UUID = UUIDv7.randomUUID(),
            year: Year,
            payoutOffset: BigDecimal,
        ) = GamePayoutOffset(
            id = id,
            year = year,
            payoutOffset = payoutOffset,
        )
    }

    fun updatePayoutOffset(newPayoutOffset: BigDecimal) {
        this.payoutOffset = newPayoutOffset
    }
}

@Repository
interface GamePayoutOffsetRepository : JpaRepository<GamePayoutOffset, UUID> {
    fun findByYear(year: Year): GamePayoutOffset?
}
