package com.cleevio.fundedmind.domain.gamepayoutoffset

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Year

@Service
class CreateGamePayoutOffsetService(
    private val gamePayoutOffsetRepository: GamePayoutOffsetRepository,
) {

    @Transactional
    fun create(
        year: Year,
        offset: BigDecimal,
    ): GamePayoutOffset = gamePayoutOffsetRepository.save(
        GamePayoutOffset.newGamePayoutOffset(
            year = year,
            payoutOffset = offset,
        ),
    )
}
