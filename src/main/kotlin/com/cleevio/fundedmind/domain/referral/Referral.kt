package com.cleevio.fundedmind.domain.referral

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.application.common.util.replaceContents
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.referral.exception.ReferralMissingPictureException
import com.cleevio.fundedmind.domain.referral.exception.ReferralOrderCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Referral is a advertisement/news/reminder/discount/update banner
 * created by admin and is visible in student dashboard on the top of the page.
 */
@Table(name = "referral")
@Entity
@DynamicUpdate
class Referral private constructor(
    id: UUID,
    listingOrder: Int,
    published: Boolean,
    imageDesktopFileId: UUID?,
    imageMobileFileId: UUID?,
    title: String?,
    description: String?,
    visibleToTiers: List<StudentTier>,
    visibleToDiscordUsers: Boolean,
    linkUrl: String?,
    rewardCouponCode: String?,
) : SoftDeletableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
    }

    var listingOrder: Int = listingOrder
        private set

    var published: Boolean = published
        private set

    @File(type = FileType.REFERRAL_DESKTOP_PHOTO)
    var imageDesktopFileId: UUID? = imageDesktopFileId
        private set

    @File(type = FileType.REFERRAL_MOBILE_PHOTO)
    var imageMobileFileId: UUID? = imageMobileFileId
        private set

    var title: String? = title
        private set

    var description: String? = description
        private set

    var visibleToDiscordUsers: Boolean = visibleToDiscordUsers
        private set

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "text[]")
    private val _visibleToTiers: MutableList<StudentTier> = visibleToTiers.toMutableList()
    val visibleToTiers: List<StudentTier>
        get() = _visibleToTiers.toList()

    var linkUrl: String? = linkUrl
        private set

    var rewardCouponCode: String? = rewardCouponCode
        private set

    companion object {
        fun newReferral(
            id: UUID = UUIDv7.randomUUID(),
            listingOrder: Int,
            title: String?,
            description: String?,
            visibleToTiers: List<StudentTier>,
            visibleToDiscordUsers: Boolean,
            linkUrl: String?,
            rewardCouponCode: String?,
        ) = Referral(
            id = id,
            listingOrder = listingOrder,
            published = false,
            title = title,
            description = description,
            visibleToDiscordUsers = visibleToDiscordUsers,
            visibleToTiers = visibleToTiers,
            linkUrl = linkUrl,
            rewardCouponCode = rewardCouponCode,
            imageDesktopFileId = null,
            imageMobileFileId = null,
        )
    }

    fun changeImageDesktop(fileId: UUID?) {
        this.imageDesktopFileId = fileId
    }

    fun changeImageMobile(fileId: UUID?) {
        this.imageMobileFileId = fileId
    }

    fun hide() {
        this.published = false
    }

    fun publish() {
        checkImagesPresent()
        this.published = true
    }

    private fun checkImagesPresent() {
        if (imageDesktopFileId == null) {
            throw ReferralMissingPictureException("Referral: '$id' does not have DESKTOP picture")
        }
        if (imageMobileFileId == null) {
            throw ReferralMissingPictureException("Referral: '$id' does not have MOBILE picture")
        }
    }

    fun updateListingOrder(newOrder: Int) {
        checkListingOrderIsPositiveOrZero(newOrder)
        this.listingOrder = newOrder
    }

    fun update(
        title: String?,
        description: String?,
        visibleToTiers: List<StudentTier>,
        visibleToDiscordUsers: Boolean,
        linkUrl: String?,
        rewardCouponCode: String?,
    ) {
        this.title = title
        this.description = description
        this._visibleToTiers.replaceContents(visibleToTiers)
        this.visibleToDiscordUsers = visibleToDiscordUsers
        this.linkUrl = linkUrl
        this.rewardCouponCode = rewardCouponCode
    }

    private fun checkListingOrderIsPositiveOrZero(listingOrder: Int) {
        if (listingOrder >= 0) return

        throw ReferralOrderCannotBeNegativeException("Listing order cannot be negative: '$listingOrder'")
    }

    fun isVisibleFor(
        userRole: UserRole,
        studentTier: StudentTier?,
        discordAccess: Boolean?,
    ): Boolean = when (userRole) {
        UserRole.ADMIN -> true

        UserRole.TRADER -> this.published

        UserRole.STUDENT -> {
            val studentHasAccess = (requireNotNull(studentTier) in visibleToTiers) ||
                (visibleToDiscordUsers && requireNotNull(discordAccess))

            this.published && studentHasAccess
        }
    }
}

@Repository
interface ReferralRepository : JpaRepository<Referral, UUID> {
    fun findAllByDeletedAtIsNull(): List<Referral>

    @Query(
        """
        SELECT MAX(r.listingOrder)
        FROM Referral r
        WHERE r.deletedAt IS NULL
    """,
    )
    fun findMaxListingOrderNonDeleted(): Int?
}
