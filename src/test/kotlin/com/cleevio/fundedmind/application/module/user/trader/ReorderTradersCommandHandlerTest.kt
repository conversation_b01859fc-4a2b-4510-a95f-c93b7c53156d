package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.trader.command.ReorderTradersCommand
import com.cleevio.fundedmind.application.module.user.trader.command.ReorderTradersCommand.TraderOrderingInput
import com.cleevio.fundedmind.application.module.user.trader.exception.ActiveTradersMismatchException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderOrderCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderTradersCommandHandlerTest(
    @Autowired private val underTest: ReorderTradersCommandHandler,
    @Autowired private val traderRepository: TraderRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder traders`() {
        dataHelper.getTrader(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getTrader(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getTrader(id = 3.toUUID(), listingOrder = 3)

        underTest.handle(
            ReorderTradersCommand(
                traderOrderings = listOf(
                    TraderOrderingInput(traderId = 1.toUUID(), newListingOrder = 2),
                    TraderOrderingInput(traderId = 2.toUUID(), newListingOrder = 3),
                    TraderOrderingInput(traderId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val traders = traderRepository.findAll()
        traders shouldHaveSize 3
        traders.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        traders.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        traders.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder traders even if display order is not unique`() {
        dataHelper.getTrader(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getTrader(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getTrader(id = 3.toUUID(), listingOrder = 3)

        underTest.handle(
            ReorderTradersCommand(
                traderOrderings = listOf(
                    TraderOrderingInput(traderId = 1.toUUID(), newListingOrder = 1),
                    TraderOrderingInput(traderId = 2.toUUID(), newListingOrder = 1),
                    TraderOrderingInput(traderId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        val traders = traderRepository.findAll()
        traders shouldHaveSize 3
        traders.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        traders.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        traders.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of traders - trader is missing`() {
        dataHelper.getTrader(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getTrader(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getTrader(id = 3.toUUID(), listingOrder = 3)

        shouldThrow<ActiveTradersMismatchException> {
            underTest.handle(
                ReorderTradersCommand(
                    traderOrderings = listOf(
                        TraderOrderingInput(traderId = 1.toUUID(), newListingOrder = 2),
                        TraderOrderingInput(traderId = 3.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is not positive or zero`() {
        dataHelper.getTrader(id = 1.toUUID(), listingOrder = 1)

        shouldThrow<TraderOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderTradersCommand(
                    traderOrderings = listOf(
                        TraderOrderingInput(traderId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
