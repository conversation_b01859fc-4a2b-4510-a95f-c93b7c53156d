package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.ListAttachmentsOfPublishedCourseQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseAttachmentsNotAccessibleToStudentException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentValue
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListAttachmentsOfPublishedCourseQueryHandlerTest(
    @Autowired private val underTest: ListAttachmentsOfPublishedCourseQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list attachments - verify mappings`() {
        // given
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // module with 2 lessons and 3 attachments
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            title = "Module 1",
            listingOrder = 1,
        ).also { module1 ->
            // lesson with 2 attachments
            dataHelper.getLesson(
                title = "Lesson 1",
                listingOrder = 2,
                courseModuleId = module1.id,
            ).also { lesson ->
                dataHelper.getLessonAttachment(
                    id = 111.toUUID(),
                    displayOrder = 2,
                    lessonId = lesson.id,
                    name = "Attachment 111",
                    type = LessonAttachmentType.PDF,
                    entityModifier = {
                        it.changeAttachmentDocument(
                            dataHelper.getDocument(
                                id = 1110.toUUID(),
                                type = FileType.LESSON_ATTACHMENT,
                            ).id,
                        )
                    },
                )
                dataHelper.getLessonAttachment(
                    id = 112.toUUID(),
                    displayOrder = 1,
                    lessonId = lesson.id,
                    name = "Attachment 112",
                    type = LessonAttachmentType.XLSX,
                    entityModifier = {
                        it.changeAttachmentDocument(
                            dataHelper.getDocument(
                                id = 1120.toUUID(),
                                type = FileType.LESSON_ATTACHMENT,
                            ).id,
                        )
                    },
                )
            }

            // lesson with 1 attachment
            dataHelper.getLesson(
                title = "Lesson 2",
                courseModuleId = module1.id,
                listingOrder = 1,
            ).also { lesson ->
                dataHelper.getLessonAttachment(
                    id = 211.toUUID(),
                    displayOrder = 1,
                    lessonId = lesson.id,
                    name = "Attachment 211",
                    type = LessonAttachmentType.PDF,
                    entityModifier = {
                        it.changeAttachmentDocument(
                            dataHelper.getDocument(
                                id = 2110.toUUID(),
                                type = FileType.LESSON_ATTACHMENT,
                            ).id,
                        )
                    },
                )
            }
        }

        // module with 1 lesson and 0 attachments
        dataHelper.getCourseModule(
            id = 2.toUUID(),
            courseId = course.id,
            title = "Module 2",
            listingOrder = 2,
        ).also { module2 ->
            dataHelper.getLesson(
                title = "Lesson 3",
                listingOrder = 1,
                courseModuleId = module2.id,
                attachments = listOf(),
            )
        }

        // when
        val result = underTest.handle(
            ListAttachmentsOfPublishedCourseQuery(
                userId = 0.toUUID(),
                courseId = course.id,
            ),
        )

        // then
        result.data shouldHaveSize 2
        result.data.first { it.moduleId == 1.toUUID() }.run {
            listingOrder shouldBe 1
            title shouldBe "Module 1"
            attachments shouldHaveSize 3
            attachments.first { it.attachmentId == 111.toUUID() }.run {
                displayOrder shouldBe 2
                nameWithExtension shouldBe "Attachment 111"
                document shouldNotBe null
                document!!.documentId shouldBe 1110.toUUID()
                type shouldBe LessonAttachmentType.PDF
                lesson.run {
                    title shouldBe "Lesson 1"
                    listingOrder shouldBe 2
                }
            }
            attachments.first { it.attachmentId == 112.toUUID() }.run {
                displayOrder shouldBe 1
                nameWithExtension shouldBe "Attachment 112"
                document shouldNotBe null
                document!!.documentId shouldBe 1120.toUUID()
                type shouldBe LessonAttachmentType.XLSX
                lesson.run {
                    title shouldBe "Lesson 1"
                    listingOrder shouldBe 2
                }
            }
            attachments.first { it.attachmentId == 211.toUUID() }.run {
                displayOrder shouldBe 1
                nameWithExtension shouldBe "Attachment 211"
                document shouldNotBe null
                document!!.documentId shouldBe 2110.toUUID()
                type shouldBe LessonAttachmentType.PDF
                lesson.run {
                    title shouldBe "Lesson 2"
                    listingOrder shouldBe 1
                }
            }
        }
        result.data.first { it.moduleId == 2.toUUID() }.run {
            moduleId shouldBe 2.toUUID()
            listingOrder shouldBe 2
            title shouldBe "Module 2"
            attachments shouldHaveSize 0
        }
    }

    @Test
    fun `should list attachments in correct order`() {
        // given
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.MASTERCLASS, entityModifier = {
            it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
        })
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            entityModifier = { it.createPicturesAndPublish() },
        )

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id, listingOrder = 2).also {
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = it.id, listingOrder = 2).also {
                dataHelper.getLessonAttachment(id = 111.toUUID(), lessonId = it.id, displayOrder = 2)
                dataHelper.getLessonAttachment(id = 112.toUUID(), lessonId = it.id, displayOrder = 3)
                dataHelper.getLessonAttachment(id = 113.toUUID(), lessonId = it.id, displayOrder = 1)
            }

            dataHelper.getLesson(id = 12.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonAttachment(id = 121.toUUID(), lessonId = it.id, displayOrder = 2)
                dataHelper.getLessonAttachment(id = 122.toUUID(), lessonId = it.id, displayOrder = 1)
            }
        }

        dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id, listingOrder = 1).also {
            dataHelper.getLesson(id = 21.toUUID(), courseModuleId = it.id, listingOrder = 1).also {
                dataHelper.getLessonAttachment(id = 211.toUUID(), lessonId = it.id, displayOrder = 1)
                dataHelper.getLessonAttachment(id = 212.toUUID(), lessonId = it.id, displayOrder = 2)
            }
        }

        // when
        val result = underTest.handle(
            ListAttachmentsOfPublishedCourseQuery(
                userId = 0.toUUID(),
                courseId = course.id,
            ),
        )

        result.data shouldHaveSize 2
        // verify order of modules
        result.data.map { it.moduleId } shouldBe listOf(2.toUUID(), 1.toUUID())

        result.data.first { it.moduleId == 1.toUUID() }.run {
            listingOrder shouldBe 2
            attachments shouldHaveSize 5
            // verify order of attachments within module
            attachments.map { it.attachmentId } shouldBe
                listOf(122.toUUID(), 121.toUUID(), 113.toUUID(), 111.toUUID(), 112.toUUID())
        }
        result.data.first { it.moduleId == 2.toUUID() }.run {
            listingOrder shouldBe 1
            attachments shouldHaveSize 2
            // verify order of attachments within module
            attachments.map { it.attachmentId } shouldBe listOf(211.toUUID(), 212.toUUID())
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = ["ADMIN", "TRADER"],
        mode = EnumSource.Mode.INCLUDE,
    )
    fun `should list attachments - admin and trader have access to all courses`(userRole: UserRole) {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = userRole)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id).also { module ->
                dataHelper.getLesson(
                    id = 11.toUUID(),
                    courseModuleId = module.id,
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "Attachment 11",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
            }
        }

        // expect no exception
        underTest.handle(
            ListAttachmentsOfPublishedCourseQuery(
                userId = user.id,
                courseId = course.id,
            ),
        )
    }

    @ParameterizedTest
    @EnumSource(
        value = StudentTier::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["MASTERCLASS", "EXCLUSIVE"],
    )
    fun `should throw if student has not access to listing attachments of published course`(studentTier: StudentTier) {
        // given
        dataHelper.getStudent(id = 0.toUUID(), studentTier = StudentTier.BASECAMP)
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(1.toUUID()).id,
            visibleToTiers = listOf(studentTier),
            entityModifier = { it.createPicturesAndPublish() },
        )

        // when/then
        shouldThrow<CourseAttachmentsNotAccessibleToStudentException> {
            underTest.handle(
                ListAttachmentsOfPublishedCourseQuery(
                    userId = 0.toUUID(),
                    courseId = course.id,
                ),
            )
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(
            fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id,
        )
        changeIntroPictureMobile(
            fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id,
        )
        publish()
    }
}
