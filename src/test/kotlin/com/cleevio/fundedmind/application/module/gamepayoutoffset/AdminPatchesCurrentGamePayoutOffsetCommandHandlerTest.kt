package com.cleevio.fundedmind.application.module.gamepayoutoffset

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayoutoffset.command.AdminPatchesCurrentGamePayoutOffsetCommand
import com.cleevio.fundedmind.application.module.gamepayoutoffset.exception.GamePayoutOffsetNotFoundException
import com.cleevio.fundedmind.domain.gamepayoutoffset.GamePayoutOffsetRepository
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.Year

class AdminPatchesCurrentGamePayoutOffsetCommandHandlerTest(
    @Autowired private val underTest: AdminPatchesCurrentGamePayoutOffsetCommandHandler,
    @Autowired private val gamePayoutOffsetRepository: GamePayoutOffsetRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing game payout offset`() {
        val existingOffset = dataHelper.getGamePayoutOffset(
            year = Year.of(2023),
            payoutOffset = BigDecimal.ZERO,
        )

        underTest.handle(
            AdminPatchesCurrentGamePayoutOffsetCommand(
                year = Year.of(2023),
                offset = BigDecimal("10.50"),
            ),
        )

        gamePayoutOffsetRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("10.50")
        }
    }

    @Test
    fun `should update existing game payout offset with negative value`() {
        val existingOffset = dataHelper.getGamePayoutOffset(
            year = Year.of(2023),
            payoutOffset = BigDecimal.ZERO,
        )

        underTest.handle(
            AdminPatchesCurrentGamePayoutOffsetCommand(
                year = Year.of(2023),
                offset = BigDecimal("-10.50"),
            ),
        )

        gamePayoutOffsetRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("-10.50")
        }
    }

    @Test
    fun `should throw exception when game payout offset not found`() {
        shouldThrow<GamePayoutOffsetNotFoundException> {
            underTest.handle(
                AdminPatchesCurrentGamePayoutOffsetCommand(
                    year = Year.of(1999),
                    offset = BigDecimal("5.00"),
                ),
            )
        }
    }
}
