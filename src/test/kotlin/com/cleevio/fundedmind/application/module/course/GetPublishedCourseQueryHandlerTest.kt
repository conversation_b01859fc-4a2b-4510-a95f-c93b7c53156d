package com.cleevio.fundedmind.application.module.course

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.course.query.GetPublishedCourseQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.CourseState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseNotFoundException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentValue
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetPublishedCourseQueryHandlerTest(
    @Autowired private val underTest: GetPublishedCourseQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get published course - verify mappings`() {
        // given
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "Joe",
            lastName = "Doe",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            tags = listOf(TraderTag.CRYPTO, TraderTag.ETF),
            biography = null,
            socialLinkInstagram = "Instagram",
            socialLinkLinkedin = "Linkedin",
            socialLinkFacebook = "Facebook",
            socialLinkTwitter = "Twitter",
            calendlyUrl = "calendly-url",
            calendlyUserUri = "user-uri",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also { trader ->
            // trader with at least one saleable product has mentoring=true
            dataHelper.getProduct(traderId = trader.id, entityModifier = { it.makeSaleable() })
            dataHelper.getProduct(traderId = trader.id, entityModifier = { it.makeUnsaleable() })
            dataHelper.getProduct(traderId = trader.id, entityModifier = { it.softDelete() })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = 1.toUUID(),
            title = "Course",
            description = "Description",
            trailerUrl = "trailer-url",
            color = Color.GOLD,
            courseCategory = CourseCategory.TRADING_BASICS,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // module with 2 lessons and 1 deleted lesson
            dataHelper.getCourseModule(id = 11.toUUID(), courseId = course.id, listingOrder = 1).also { module1 ->
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    listingOrder = 1,
                    entityModifier = { it.softDelete() },
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "deleted",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
                dataHelper.getLesson(
                    id = 111.toUUID(),
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    listingOrder = 1,
                )
                dataHelper.getLesson(
                    courseModuleId = module1.id,
                    durationInSeconds = 10,
                    listingOrder = 2,
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "1",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
            }
            // module with 1 lesson
            dataHelper.getCourseModule(courseId = course.id, listingOrder = 2).also { module2 ->
                dataHelper.getLesson(
                    courseModuleId = module2.id,
                    durationInSeconds = 100,
                    listingOrder = 1,
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "2",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
            }
            // module with 1 deleted lesson
            dataHelper.getCourseModule(courseId = course.id, listingOrder = 3).also { module3 ->
                dataHelper.getLesson(
                    courseModuleId = module3.id,
                    durationInSeconds = 1000,
                    listingOrder = 1,
                    entityModifier = { it.softDelete() },
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "deleted 2",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
            }
            // deleted module with deleted lesson
            dataHelper.getCourseModule(
                id = 4.toUUID(),
                courseId = course.id,
                listingOrder = 4,
                entityModifier = { it.softDelete() },
            ).also { module4 ->
                dataHelper.getLesson(
                    courseModuleId = module4.id,
                    durationInSeconds = 10000,
                    listingOrder = 1,
                    entityModifier = { it.softDelete() },
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "deleted 3",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                )
            }
        }

        // when
        val result = underTest.handle(
            GetPublishedCourseQuery(
                userId = user.id,
                courseId = course.id,
            ),
        )

        // then
        result.run {
            courseId shouldBe 1.toUUID()
            courseCategory shouldBe CourseCategory.TRADING_BASICS
            traderInfo.run {
                traderId shouldBe 1.toUUID()
                position shouldBe "Mentor"
                firstName shouldBe "Joe"
                lastName shouldBe "Doe"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "url"
                    imageCompressedUrl shouldBe "url-comp"
                    imageBlurHash shouldBe "123"
                }
                badgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
                biography shouldBe null
                tags shouldBe listOf(TraderTag.CRYPTO, TraderTag.ETF)
                socialLinkInstagram shouldBe "Instagram"
                socialLinkLinkedin shouldBe "Linkedin"
                socialLinkFacebook shouldBe "Facebook"
                socialLinkTwitter shouldBe "Twitter"
                calendlyUrl shouldBe "calendly-url"
                mentoring shouldBe TraderMentoring.YES
            }
            color shouldBe Color.GOLD
            trailerUrl shouldBe "trailer-url"
            title shouldBe "Course"
            moduleCount shouldBe 3
            attachmentCount shouldBe 2
            totalDurationInSeconds shouldBe 120
            description shouldBe "Description"
            introPictureDesktop shouldNotBe null
            introPictureMobile shouldNotBe null
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
            unlockedData!!.run {
                courseState shouldBe CourseState.NOT_STARTED
                lessonToWatch shouldNotBe null
                lessonToWatch!!.run {
                    lessonId shouldBe 111.toUUID()
                    courseModuleId shouldBe 11.toUUID()
                    courseId shouldBe 1.toUUID()
                }
            }
        }
    }

    @Test
    fun `should get published course with correct in progress state`() {
        // given
        val user1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }
        val user2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            // module with 2 lessons
            dataHelper.getCourseModule(courseId = course.id, listingOrder = 1).also { module1 ->
                dataHelper.getLesson(
                    id = 111.toUUID(),
                    courseModuleId = module1.id,
                    listingOrder = 1,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                    dataHelper.getLessonProgress(
                        userId = user2.id,
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
                dataHelper.getLesson(
                    id = 112.toUUID(),
                    courseModuleId = module1.id,
                    listingOrder = 2,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        updatedTimestamp = "2025-01-01T10:00:00Z".toInstant(), // 01.01. 10:00
                    )
                }
            }
            // module with 1 lesson
            dataHelper.getCourseModule(id = 21.toUUID(), courseId = course.id, listingOrder = 2).also { module2 ->
                dataHelper.getLesson(
                    id = 211.toUUID(),
                    courseModuleId = module2.id,
                    listingOrder = 1,
                    attachments = listOf(
                        LessonAttachmentValue(
                            name = "2",
                            type = LessonAttachmentType.PDF,
                            displayOrder = 1,
                        ),
                    ),
                ).also { lesson ->
                    // this lesson is the last watched lesson by user1
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        seconds = 10,
                        updatedTimestamp = "2025-01-02T10:00:00Z".toInstant(), // 02.01. 10:00
                    )
                }
            }
        }

        // when
        val result = underTest.handle(
            GetPublishedCourseQuery(
                userId = user1.id,
                courseId = course.id,
            ),
        )

        // then
        result.run {
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
            unlockedData!!.run {
                courseState shouldBe CourseState.IN_PROGRESS
                lessonToWatch shouldNotBe null
                lessonToWatch!!.run {
                    lessonId shouldBe 211.toUUID()
                    courseModuleId shouldBe 21.toUUID()
                    courseId shouldBe 1.toUUID()
                }
            }
        }
    }

    @Test
    fun `should get published course with correct finished state even if user is re-watching finished lesson`() {
        // given
        val user1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            entityModifier = { it.createPicturesAndPublish() },
        ).also { course ->
            dataHelper.getCourseModule(courseId = course.id, listingOrder = 1).also { module1 ->
                dataHelper.getLesson(
                    id = 111.toUUID(),
                    courseModuleId = module1.id,
                    listingOrder = 1,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        entityModifier = { it.finish(finishedAt) }, // 01.01. 10:00
                        // user has finished lesson but is re-watching it day later
                        updatedTimestamp = finishedAt.plus(1, ChronoUnit.DAYS), // 02.01. 10:00
                    )
                }

                dataHelper.getCourseModuleProgress(
                    userId = user1.id,
                    courseModuleId = module1.id,
                    finishedAt = finishedAt, // 01.01. 10:00
                )
            }

            dataHelper.getCourseProgress(
                userId = user1.id,
                courseId = course.id,
                finishedAt = finishedAt,
            )
        }

        // when
        val result = underTest.handle(
            GetPublishedCourseQuery(
                userId = user1.id,
                courseId = course.id,
            ),
        )

        // then
        result.run {
            isLockedForMe shouldBe false
            unlockedData shouldNotBe null
            unlockedData!!.run {
                courseState shouldBe CourseState.FINISHED
                lessonToWatch shouldBe null
            }
        }
    }

    @Test
    fun `should throw if course is deleted`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            entityModifier = {
                it.createPicturesAndPublish()
                it.softDelete()
            },
        )

        // when/then
        shouldThrow<CourseNotFoundException> {
            underTest.handle(
                GetPublishedCourseQuery(
                    userId = 0.toUUID(),
                    courseId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if course is not published`() {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.TRADER)
        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
        )

        // when/then
        shouldThrow<CourseNotFoundException> {
            underTest.handle(
                GetPublishedCourseQuery(
                    userId = 0.toUUID(),
                    courseId = 1.toUUID(),
                ),
            )
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = UserRole::class,
        names = ["ADMIN", "TRADER"],
        mode = EnumSource.Mode.INCLUDE,
    )
    fun `should get published course - admin and trader have access to all courses`(userRole: UserRole) {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = userRole)

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            visibleToTiers = listOf(StudentTier.EXCLUSIVE),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // when
        val result = underTest.handle(
            GetPublishedCourseQuery(
                userId = 0.toUUID(),
                courseId = 1.toUUID(),
            ),
        )

        // then
        result.isLockedForMe shouldBe false
        result.unlockedData shouldNotBe null
    }

    @ParameterizedTest
    @EnumSource(
        value = StudentTier::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["MASTERCLASS", "EXCLUSIVE"],
    )
    fun `should get published course - should lock based on student tier`(studentTier: StudentTier) {
        // given
        dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = 0.toUUID(), studentTier = studentTier)

        dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader(id = 1.toUUID()).id,
            visibleToTiers = listOf(StudentTier.BASECAMP),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        // when
        val result = underTest.handle(
            GetPublishedCourseQuery(
                userId = 0.toUUID(),
                courseId = 1.toUUID(),
            ),
        )

        // then
        result.isLockedForMe shouldBe true
        result.unlockedData shouldBe null
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(
            fileId = dataHelper.getImage(
                type = FileType.COURSE_DESKTOP_INTRO_PHOTO,
            ).id,
        )
        changeIntroPictureMobile(
            fileId = dataHelper.getImage(
                type = FileType.COURSE_MOBILE_INTRO_PHOTO,
            ).id,
        )
        publish()
    }
}
