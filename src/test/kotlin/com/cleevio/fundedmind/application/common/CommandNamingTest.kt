package com.cleevio.fundedmind.application.common

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.command.ImageResult
import io.kotest.assertions.withClue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.swagger.v3.oas.annotations.media.Schema
import org.junit.jupiter.api.Test
import java.lang.reflect.ParameterizedType
import kotlin.reflect.KClass
import kotlin.reflect.full.findAnnotation
import kotlin.test.fail

class CommandNamingTest : ApplicationStructureTest() {

    @Test
    fun `all Command data classes must conform to the predefined structure and naming`() {
        val commandClasses = scanResult.getClassesImplementing(Command::class.java.name)

        for (classInfo in commandClasses) {
            val clazz = classInfo.loadClass()
            val kClass: KClass<out Any> = clazz.kotlin

            withClue("Class '${kClass.simpleName}' implementing Command must be a data class") {
                kClass.isData shouldBe true
            }

            withClue("Class ${kClass.simpleName} must have suffix Command in its name") {
                kClass.simpleName!!.endsWith("Command") shouldBe true
            }

            // We want to check the generic type T in Command<T>
            // We'll fetch the class's generic interfaces and find the Command<T> one and assert that it has a type
            val resultType = clazz.genericInterfaces
                .filterIsInstance<ParameterizedType>()
                .find { it.rawType == Command::class.java }
                ?.actualTypeArguments
                ?.firstOrNull()
                ?: fail("Class '${kClass.simpleName}' implementing Command must have a type parameter")

            val nestedResultClass = kClass.nestedClasses.find { it.simpleName!!.endsWith("Result") }

            when (resultType) {
                Unit::class.java -> {
                    withClue("Command<Unit> class ${kClass.simpleName} should NOT have a nested 'Result' class") {
                        nestedResultClass shouldBe null
                    }
                }

                IdResult::class.java -> {
                    withClue("Command<IdResult> class ${kClass.simpleName} should NOT have a nested 'Result' class") {
                        nestedResultClass shouldBe null
                    }
                }

                ImageResult::class.java -> {
                    withClue(
                        "Command<ImageResult> class ${kClass.simpleName} should NOT have a nested 'Result' class",
                    ) {
                        nestedResultClass shouldBe null
                    }
                }

                DocumentResult::class.java -> {
                    withClue(
                        "Command<DocumentResult> class ${kClass.simpleName} should NOT have a nested 'Result' class",
                    ) {
                        nestedResultClass shouldBe null
                    }
                }

                else -> {
                    withClue(
                        "Class ${kClass.simpleName} must have a nested 'Result' class because it's Command<Non-Unit>",
                    ) {
                        nestedResultClass shouldNotBe null
                    }

                    val schemaAnnotation = nestedResultClass!!.findAnnotation<Schema>()
                    withClue("Nested 'Result' class in ${kClass.simpleName} must have a @Schema annotation") {
                        schemaAnnotation shouldNotBe null
                    }

                    val expectedSchemaName = kClass.simpleName!!.removeSuffix("Command") + "Result"

                    withClue(
                        "The @Schema(name) in '${kClass.simpleName}.Result' should match use case name + 'Result' suffix",
                    ) {
                        schemaAnnotation?.name shouldBe expectedSchemaName
                    }
                }
            }
        }
    }
}
